"""
Enhanced MCP Manager with Bedrock Session Management
Async-first mixin aligned with Bedrock-backed session_manager integration.
- Uses ChatSession for context/history
- Builds valid Converse toolConfig (JSON schema object)
- Persists turns to Bedrock via ChatSession.add_turn (which writes invocation steps)
"""

import logging
import os
from typing import Dict, List, Any, Optional

from session_manager_new import session_manager  # updated import

logger = logging.getLogger(__name__)


class EnhancedMCPMixin:
    """
    Mixin class to add Bedrock session-aware functionality to existing MCPClientManager.
    Uses native Bedrock session management (via session_manager) for context retention.
    """

    # Provide a model id or get from config
    model_id: str = "anthropic.claude-3-5-sonnet-20240620-v1:0"

    async def chat_with_bedrock_with_context(
        self,
        message: str,
        session_id: str,
        tools_available: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Enhanced Bedrock chat with native session context retention using session_manager.
        """
        try:
            # Get or create Bedrock-backed chat session
            chat_session = session_manager.get_or_create_session(session_id)

            # Build messages with recent history
            historical_messages = chat_session.get_bedrock_messages(max_turns=8)
            current_messages = historical_messages + [{
                "role": "user",
                "content": [{"text": message}]
            }]

            # System message with session context
            system_message = self._build_context_aware_system_message(chat_session, tools_available)

            # Tool configuration for Converse API (valid JSON schema object)
            tool_config = self._build_tool_config_for_bedrock(tools_available)

            # Execute conversation via existing Bedrock client (async)
            result = await self._execute_contextual_conversation(
                messages=current_messages,
                system_message=system_message,
                tool_config=tool_config,
                session_id=session_id,
                model_id=self.model_id,
            )

            # Persist turn (ChatSession handles Bedrock steps with minimal tool metadata)
            chat_session.add_turn(
                user_message=message,
                assistant_response=result["response"],
                tools_used=result.get("tools_used", []),
            )

            logger.info(f"Completed contextual chat for session {session_id}: {len(result.get('tools_used', []))} tools used")
            return {
                "response": result["response"],
                "tools_used": result.get("tools_used", []),
                "session_id": session_id
            }

        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error in contextual chat for session {session_id}: {error_msg}")

            # Provide specific error guidance
            if "ValidationException" in error_msg:
                if "model" in error_msg.lower():
                    response_text = f"Model validation error. Please check your BEDROCK_MODEL_ID in .env file. Current model: {self.model_id}"
                elif "region" in error_msg.lower():
                    response_text = f"Region validation error. Please check your AWS_REGION configuration. Current region: {os.getenv('AWS_REGION', 'not set')}"
                else:
                    response_text = f"Bedrock API validation error: {error_msg}"
            elif "RetryError" in error_msg:
                response_text = "Connection retry failed. Please check your AWS credentials and region configuration."
            else:
                response_text = f"I apologize, but I encountered an error: {error_msg}"

            return {
                "response": response_text,
                "tools_used": [],
                "session_id": session_id,
                "error": True
            }

    def _build_context_aware_system_message(
        self,
        chat_session,
        tools_available: Optional[List[str]] = None
    ) -> str:
        """
        Compose a concise system prompt that includes session context summary and tool availability hints.
        """
        context = chat_session.get_context_for_bedrock()
        tool_hint = ""
        if tools_available:
            tool_hint = (
                "\nYou can use tools when needed. "
                "Only call tools if required and keep inputs minimal."
            )
        system = (
            "You are an assistant inside an MCP Bot. "
            "Answer precisely, and call tools only if necessary."
            f"\n\nSession Context:\n{context}"
            f"{tool_hint}"
        )
        return system

    def _build_tool_config_for_bedrock(self, tools_available: Optional[List[str]] = None) -> Optional[Dict]:
        """
        Build Bedrock Converse toolConfig; inputSchema.json must be a JSON object per API contract.
        """
        if not tools_available:
            return None

        available_tools = self.get_available_tools()
        tools: List[Dict[str, Any]] = []

        for tool_key in tools_available:
            if tool_key not in available_tools:
                continue

            tool_data = available_tools[tool_key]
            tool = tool_data["tool"]

            # Ensure proper JSON schema shape (object at top level)
            input_schema = tool.get("input_schema") or {"type": "object", "properties": {}, "required": []}
            if "type" not in input_schema:
                input_schema["type"] = "object"
            if "properties" not in input_schema:
                input_schema["properties"] = {}

            tools.append({
                "toolSpec": {
                    "name": tool["name"],
                    "description": tool.get("description") or f"Tool from server {tool_data['server']}",
                    "inputSchema": {
                        "json": input_schema
                    }
                }
            })

        if not tools:
            return None

        return {"tools": tools, "toolChoice": {"auto": {}}}

    async def _execute_contextual_conversation(
        self,
        messages: List[Dict[str, Any]],
        system_message: str,
        tool_config: Optional[Dict],
        session_id: str,
        model_id: str,
    ) -> Dict[str, Any]:
        """
        Minimal async Converse loop with optional tool calling (two-pass toolUse -> toolResult).
        """
        runtime = await self.get_async_bedrock_runtime()
        request = {
            "modelId": model_id,
            "messages": [{"role": "system", "content": [{"text": system_message}]}] + messages,
        }
        if tool_config:
            request["toolConfig"] = tool_config

        resp = await runtime.converse(**request)
        content = resp.get("output", {}).get("message", {}).get("content", [])

        # If model requested tools, execute and send back tool results
        tool_uses = [b.get("toolUse") for b in content if "toolUse" in b]
        tools_used: List[Dict[str, Any]] = []
        if tool_uses:
            planned_calls = [{"name": tu["name"], "input": tu.get("input", {}), "toolUseId": tu.get("toolUseId")} for tu in tool_uses]
            exec_results = await self._execute_tool_calls(planned_calls, session_id)

            # Build toolResult content blocks (no extra fields per schema)
            tool_results_blocks = []
            for call, res in zip(planned_calls, exec_results):
                out_payload = {"success": res.get("success", False)}
                if res.get("success"):
                    out_payload["result"] = res.get("result", "")
                else:
                    out_payload["error"] = res.get("error", "Unknown error")

                tool_results_blocks.append({
                    "toolResult": {
                        "toolUseId": call["toolUseId"],
                        "content": [{"json": out_payload}]
                    }
                })

            followup = {
                "modelId": model_id,
                "messages": [{"role": "system", "content": [{"text": system_message}]}]
                            + messages
                            + [{"role": "user", "content": tool_results_blocks}],
            }
            if tool_config:
                followup["toolConfig"] = tool_config

            resp2 = await runtime.converse(**followup)
            content = resp2.get("output", {}).get("message", {}).get("content", [])
            tools_used = exec_results

        final_text_parts = [b["text"] for b in content if "text" in b]
        final_text = "\n".join(final_text_parts).strip() if final_text_parts else ""

        return {"response": final_text, "tools_used": tools_used, "session_id": session_id}

    async def get_async_bedrock_runtime(self):
        """
        Provide an async bedrock-runtime client (aioboto3).
        Override in manager to reuse a pooled client.
        """
        import aioboto3
        session = aioboto3.Session()
        return await session.client("bedrock-runtime").__aenter__()

    async def _execute_tool_calls(self, tool_calls: List[Dict], session_id: str) -> List[Dict]:
        """
        Execute tool calls from Bedrock and return compact results (for toolResult json).
        """
        tools_used = []
        for tool_call in tool_calls:
            tool_name = tool_call.get("name")
            tool_input = tool_call.get("input", {})
            tool_use_id = tool_call.get("toolUseId")

            logger.info(f"Executing tool: {tool_name} with input: {tool_input}")

            server_name = self._find_server_for_tool(tool_name)
            if not server_name:
                logger.error(f"Server not found for tool: {tool_name}")
                tools_used.append({
                    "tool_name": tool_name,
                    "server_name": None,
                    "input": tool_input,
                    "success": False,
                    "error": f"Tool {tool_name} not found",
                    "session_id": session_id,
                    "toolUseId": tool_use_id,
                })
                continue

            try:
                result = await self.call_tool(server_name, tool_name, tool_input)
                usage = {
                    "tool_name": tool_name,
                    "server_name": server_name,
                    "input": tool_input,
                    "success": result.get("success", False),
                    "session_id": session_id,
                    "toolUseId": tool_use_id,
                }
                if usage["success"]:
                    result_str = str(result.get("result", ""))
                    usage["result"] = result_str[:500] + "..." if len(result_str) > 500 else result_str
                else:
                    usage["error"] = result.get("error", "Unknown error")
                tools_used.append(usage)
            except Exception as e:
                logger.error(f"Tool execution error for {tool_name}: {e}")
                tools_used.append({
                    "tool_name": tool_name,
                    "server_name": server_name,
                    "input": tool_input,
                    "success": False,
                    "error": str(e),
                    "session_id": session_id,
                    "toolUseId": tool_use_id,
                })

        return tools_used

    def _find_server_for_tool(self, tool_name: str) -> Optional[str]:
        """Find which server provides a specific tool"""
        available_tools = self.get_available_tools()
        for data in available_tools.values():
            if data["tool"]["name"] == tool_name:
                return data["server"]
        return None
