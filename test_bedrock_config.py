#!/usr/bin/env python3
"""
Test script to verify Bedrock configuration and diagnose issues
"""

import os
import boto3
import json
from dotenv import load_dotenv
from botocore.exceptions import ClientError, NoCredentialsError

# Load environment variables
load_dotenv()

def test_aws_credentials():
    """Test AWS credentials"""
    print("🔐 Testing AWS Credentials...")
    try:
        session = boto3.Session()
        credentials = session.get_credentials()
        if credentials:
            print("✅ AWS credentials found")
            print(f"   Access Key: {credentials.access_key[:8]}...")
            return True
        else:
            print("❌ No AWS credentials found")
            return False
    except Exception as e:
        print(f"❌ Error checking credentials: {e}")
        return False

def test_region_config():
    """Test AWS region configuration"""
    print("\n🌍 Testing AWS Region Configuration...")
    region = os.getenv("AWS_REGION")
    if region:
        print(f"✅ AWS_REGION set to: {region}")
        return region
    else:
        print("❌ AWS_REGION not set in environment")
        return None

def test_bedrock_access(region):
    """Test Bedrock service access"""
    print(f"\n🧠 Testing Bedrock Access in {region}...")
    try:
        client = boto3.client('bedrock', region_name=region)
        # Try to list foundation models
        response = client.list_foundation_models()
        models = response.get('modelSummaries', [])
        print(f"✅ Bedrock accessible - Found {len(models)} models")
        return True
    except ClientError as e:
        error_code = e.response['Error']['Code']
        print(f"❌ Bedrock access error: {error_code} - {e.response['Error']['Message']}")
        return False
    except Exception as e:
        print(f"❌ Bedrock access error: {e}")
        return False

def test_model_availability(region):
    """Test specific model availability"""
    print(f"\n🤖 Testing Model Availability...")
    model_id = os.getenv("BEDROCK_MODEL_ID", "amazon.nova-lite-v1:0")
    print(f"   Testing model: {model_id}")
    
    try:
        client = boto3.client('bedrock', region_name=region)
        response = client.list_foundation_models()
        models = response.get('modelSummaries', [])
        
        # Check if our model is available
        available_models = [model['modelId'] for model in models]
        if model_id in available_models:
            print(f"✅ Model {model_id} is available")
            return True
        else:
            print(f"❌ Model {model_id} not found")
            print("Available models:")
            for model in models[:10]:  # Show first 10 models
                print(f"   - {model['modelId']}")
            if len(models) > 10:
                print(f"   ... and {len(models) - 10} more")
            return False
    except Exception as e:
        print(f"❌ Error checking model availability: {e}")
        return False

def test_bedrock_runtime(region):
    """Test Bedrock Runtime API"""
    print(f"\n⚡ Testing Bedrock Runtime API...")
    model_id = os.getenv("BEDROCK_MODEL_ID", "amazon.nova-lite-v1:0")
    
    try:
        client = boto3.client('bedrock-runtime', region_name=region)
        
        # Simple test message
        test_request = {
            "modelId": model_id,
            "messages": [
                {
                    "role": "user",
                    "content": [{"text": "Hello, can you respond with just 'OK'?"}]
                }
            ]
        }
        
        response = client.converse(**test_request)
        content = response.get('output', {}).get('message', {}).get('content', [])
        response_text = content[0].get('text', '') if content else ''
        
        print(f"✅ Bedrock Runtime working - Response: {response_text[:50]}...")
        return True
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        error_msg = e.response['Error']['Message']
        print(f"❌ Bedrock Runtime error: {error_code} - {error_msg}")
        
        if error_code == 'ValidationException':
            print("   💡 This is likely a model ID or region issue")
        elif error_code == 'AccessDeniedException':
            print("   💡 Check your AWS permissions for Bedrock")
        
        return False
    except Exception as e:
        print(f"❌ Bedrock Runtime error: {e}")
        return False

def test_session_management(region):
    """Test Bedrock Session Management API"""
    print(f"\n📝 Testing Bedrock Session Management...")
    
    try:
        client = boto3.client('bedrock-session', region_name=region)
        
        # Try to create a test session
        response = client.create_session()
        session_id = response.get('sessionId')
        
        if session_id:
            print(f"✅ Session Management working - Created session: {session_id[:16]}...")
            
            # Clean up test session
            try:
                client.delete_session(sessionIdentifier=session_id)
                print("   ✅ Test session cleaned up")
            except:
                pass
            
            return True
        else:
            print("❌ Session creation failed - no session ID returned")
            return False
            
    except ClientError as e:
        error_code = e.response['Error']['Code']
        error_msg = e.response['Error']['Message']
        print(f"❌ Session Management error: {error_code} - {error_msg}")
        
        if 'not supported' in error_msg.lower():
            print("   💡 Session Management API may not be available in this region")
        
        return False
    except Exception as e:
        print(f"❌ Session Management error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Bedrock Configuration Test\n")
    
    # Test 1: AWS Credentials
    if not test_aws_credentials():
        print("\n❌ Cannot proceed without AWS credentials")
        return
    
    # Test 2: Region Configuration
    region = test_region_config()
    if not region:
        print("\n❌ Cannot proceed without AWS region")
        return
    
    # Test 3: Bedrock Access
    if not test_bedrock_access(region):
        print("\n❌ Cannot access Bedrock service")
        return
    
    # Test 4: Model Availability
    test_model_availability(region)
    
    # Test 5: Bedrock Runtime
    test_bedrock_runtime(region)
    
    # Test 6: Session Management
    test_session_management(region)
    
    print("\n🏁 Test completed!")
    print("\n💡 Recommendations:")
    print("   1. If model tests fail, try switching to anthropic.claude-3-5-sonnet-20240620-v1:0")
    print("   2. If session management fails, the app will still work without context retention")
    print("   3. Check AWS console for Bedrock model access permissions")

if __name__ == "__main__":
    main()
